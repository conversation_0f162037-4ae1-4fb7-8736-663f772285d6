<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Betterfly Proxy Manager - 选项</title>
  <link rel="stylesheet" href="css/options.css">
</head>
<body>
  <div class="container">
    <!-- 侧边栏 -->
    <div class="sidebar">
      <div class="logo">
        <img src="icons/icon48.png" alt="Betterfly Proxy">
        <h2>Betterfly Proxy</h2>
      </div>
      
      <nav class="nav-menu">
        <a href="#profiles" class="nav-item active" data-tab="profiles">
          <span class="icon">🌐</span>
          代理配置
        </a>
        <a href="#rules" class="nav-item" data-tab="rules">
          <span class="icon">📋</span>
          切换规则
        </a>
        <a href="#settings" class="nav-item" data-tab="settings">
          <span class="icon">⚙️</span>
          通用设置
        </a>
        <a href="#about" class="nav-item" data-tab="about">
          <span class="icon">ℹ️</span>
          关于
        </a>
      </nav>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- 代理配置页面 -->
      <div class="tab-content active" id="profiles">
        <div class="page-header">
          <h1>代理配置管理</h1>
          <button class="btn btn-primary" id="addNewProfile">
            <span class="icon">+</span>
            新建配置
          </button>
        </div>

        <div class="profiles-grid" id="profilesGrid">
          <!-- 代理配置卡片将通过JavaScript动态生成 -->
        </div>
      </div>

      <!-- 切换规则页面 -->
      <div class="tab-content" id="rules">
        <div class="page-header">
          <h1>自动切换规则</h1>
          <button class="btn btn-primary" id="addNewRule">
            <span class="icon">+</span>
            新建规则
          </button>
        </div>

        <div class="rules-list" id="rulesList">
          <div class="empty-state">
            <div class="empty-icon">📋</div>
            <h3>暂无切换规则</h3>
            <p>创建规则来自动根据网站切换代理配置</p>
            <button class="btn btn-primary" onclick="document.getElementById('addNewRule').click()">
              创建第一个规则
            </button>
          </div>
        </div>
      </div>

      <!-- 通用设置页面 -->
      <div class="tab-content" id="settings">
        <div class="page-header">
          <h1>通用设置</h1>
        </div>

        <div class="settings-sections">
          <div class="settings-section">
            <h3>代理设置</h3>
            <div class="setting-item">
              <label class="setting-label">
                <input type="checkbox" id="autoSwitchEnabled">
                <span class="checkmark"></span>
                启用自动切换规则
              </label>
              <p class="setting-description">根据访问的网站自动切换代理配置</p>
            </div>
            
            <div class="setting-item">
              <label class="setting-label">
                <input type="checkbox" id="showNotifications">
                <span class="checkmark"></span>
                显示切换通知
              </label>
              <p class="setting-description">代理切换时显示桌面通知</p>
            </div>
          </div>

          <div class="settings-section">
            <h3>界面设置</h3>
            <div class="setting-item">
              <label for="theme">主题</label>
              <select id="theme" class="setting-select">
                <option value="light">浅色主题</option>
                <option value="dark">深色主题</option>
                <option value="auto">跟随系统</option>
              </select>
            </div>
          </div>

          <div class="settings-section">
            <h3>数据管理</h3>
            <div class="setting-actions">
              <button class="btn btn-secondary" id="exportSettings">导出配置</button>
              <button class="btn btn-secondary" id="importSettings">导入配置</button>
              <button class="btn btn-danger" id="resetSettings">重置所有设置</button>
            </div>
          </div>
        </div>
      </div>

      <!-- 关于页面 -->
      <div class="tab-content" id="about">
        <div class="page-header">
          <h1>关于 Betterfly Proxy Manager</h1>
        </div>

        <div class="about-content">
          <div class="about-section">
            <h3>版本信息</h3>
            <p>版本: 1.0.0</p>
            <p>构建日期: 2025-07-02</p>
          </div>

          <div class="about-section">
            <h3>功能特性</h3>
            <ul>
              <li>支持HTTP、HTTPS、SOCKS4、SOCKS5代理</li>
              <li>支持PAC脚本自动配置</li>
              <li>快速切换代理配置</li>
              <li>自动切换规则</li>
              <li>代理连接测试</li>
              <li>配置导入导出</li>
            </ul>
          </div>

          <div class="about-section">
            <h3>开源许可</h3>
            <p>本项目基于 MIT 许可证开源</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 配置编辑模态框 -->
  <div class="modal" id="profileModal">
    <div class="modal-content large">
      <div class="modal-header">
        <h3 id="modalTitle">新建代理配置</h3>
        <button class="close-btn" id="closeProfileModal">&times;</button>
      </div>
      <div class="modal-body">
        <form id="profileForm">
          <div class="form-row">
            <div class="form-group">
              <label for="profileName">配置名称 *</label>
              <input type="text" id="profileName" placeholder="输入配置名称" required>
            </div>
            <div class="form-group">
              <label for="profileColor">颜色标识</label>
              <input type="color" id="profileColor" value="#007bff">
            </div>
          </div>

          <div class="form-group">
            <label for="proxyType">代理类型 *</label>
            <select id="proxyType" required>
              <option value="fixed_servers">HTTP/HTTPS/SOCKS代理</option>
              <option value="pac_script">PAC脚本</option>
            </select>
          </div>

          <div class="proxy-config" id="fixedServerConfig">
            <div class="form-row">
              <div class="form-group">
                <label for="proxyProtocol">协议</label>
                <select id="proxyProtocol">
                  <option value="http">HTTP</option>
                  <option value="https">HTTPS</option>
                  <option value="socks4">SOCKS4</option>
                  <option value="socks5">SOCKS5</option>
                </select>
              </div>
              <div class="form-group">
                <label for="proxyServer">服务器地址 *</label>
                <input type="text" id="proxyServer" placeholder="例如: 127.0.0.1" required>
              </div>
              <div class="form-group">
                <label for="proxyPort">端口 *</label>
                <input type="number" id="proxyPort" placeholder="例如: 8080" min="1" max="65535" required>
              </div>
            </div>

            <div class="form-group">
              <label for="proxyAuth">身份验证</label>
              <div class="auth-toggle">
                <input type="checkbox" id="enableAuth">
                <label for="enableAuth">需要用户名和密码</label>
              </div>
            </div>

            <div class="auth-fields" id="authFields" style="display: none;">
              <div class="form-row">
                <div class="form-group">
                  <label for="proxyUsername">用户名</label>
                  <input type="text" id="proxyUsername" placeholder="输入用户名">
                </div>
                <div class="form-group">
                  <label for="proxyPassword">密码</label>
                  <input type="password" id="proxyPassword" placeholder="输入密码">
                </div>
              </div>
            </div>
          </div>

          <div class="proxy-config" id="pacScriptConfig" style="display: none;">
            <div class="form-group">
              <label for="pacScript">PAC脚本内容 *</label>
              <textarea id="pacScript" placeholder="输入PAC脚本内容或URL" rows="10"></textarea>
              <div class="form-help">
                支持直接输入PAC脚本代码或PAC文件的URL地址
              </div>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" id="cancelProfile">取消</button>
        <button type="button" class="btn btn-info" id="testProfile">测试连接</button>
        <button type="submit" class="btn btn-primary" form="profileForm">保存配置</button>
      </div>
    </div>
  </div>

  <input type="file" id="importFileInput" accept=".json" style="display: none;">

  <script src="js/proxy-manager.js"></script>
  <script src="js/options.js"></script>
</body>
</html>
