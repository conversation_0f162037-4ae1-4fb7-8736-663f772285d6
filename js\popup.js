// DOM元素
const statusIndicator = document.getElementById('statusIndicator');
const profileList = document.getElementById('profileList');
const addProfileBtn = document.getElementById('addProfileBtn');
const optionsBtn = document.getElementById('optionsBtn');
const connectionInfo = document.getElementById('connectionInfo');
const addProfileModal = document.getElementById('addProfileModal');
const closeModalBtn = document.getElementById('closeModalBtn');
const cancelBtn = document.getElementById('cancelBtn');
const addProfileForm = document.getElementById('addProfileForm');
const proxyType = document.getElementById('proxyType');
const fixedServerConfig = document.getElementById('fixedServerConfig');
const pacScriptConfig = document.getElementById('pacScriptConfig');

// 当前配置和状态
let currentProfiles = {};
let currentProfileId = 'direct';

// 初始化
document.addEventListener('DOMContentLoaded', async () => {
  await loadProfiles();
  await loadCurrentProfile();
  setupEventListeners();
});

// 设置事件监听器
function setupEventListeners() {
  addProfileBtn.addEventListener('click', showAddProfileModal);
  optionsBtn.addEventListener('click', openOptionsPage);
  closeModalBtn.addEventListener('click', hideAddProfileModal);
  cancelBtn.addEventListener('click', hideAddProfileModal);
  addProfileForm.addEventListener('submit', handleAddProfile);
  proxyType.addEventListener('change', toggleProxyConfig);
  
  // 点击模态框外部关闭
  addProfileModal.addEventListener('click', (e) => {
    if (e.target === addProfileModal) {
      hideAddProfileModal();
    }
  });
}

// 加载所有代理配置
async function loadProfiles() {
  try {
    const response = await sendMessage({ action: 'getProfiles' });
    if (response.success) {
      currentProfiles = response.data;
      renderProfiles();
    }
  } catch (error) {
    console.error('Failed to load profiles:', error);
    showError('加载配置失败');
  }
}

// 加载当前激活的配置
async function loadCurrentProfile() {
  try {
    const response = await sendMessage({ action: 'getCurrentProfile' });
    if (response.success) {
      currentProfileId = response.data;
      updateStatusIndicator();
    }
  } catch (error) {
    console.error('Failed to load current profile:', error);
  }
}

// 渲染代理配置列表
function renderProfiles() {
  profileList.innerHTML = '';
  
  Object.values(currentProfiles).forEach(profile => {
    const profileElement = createProfileElement(profile);
    profileList.appendChild(profileElement);
  });
}

// 创建代理配置元素
function createProfileElement(profile) {
  const div = document.createElement('div');
  div.className = `profile-item ${profile.id === currentProfileId ? 'active' : ''}`;
  div.dataset.profileId = profile.id;
  
  const details = getProfileDetails(profile);
  
  div.innerHTML = `
    <div class="profile-color" style="background-color: ${profile.color || '#007bff'}"></div>
    <div class="profile-info">
      <div class="profile-name">${profile.name}</div>
      <div class="profile-details">${details}</div>
    </div>
    <div class="profile-actions">
      ${!['direct', 'system'].includes(profile.id) ? `
        <button class="action-btn test" title="测试连接">🔍</button>
        <button class="action-btn delete" title="删除">🗑</button>
      ` : ''}
    </div>
  `;
  
  // 添加点击事件
  div.addEventListener('click', (e) => {
    if (!e.target.classList.contains('action-btn')) {
      switchToProfile(profile.id);
    }
  });
  
  // 添加操作按钮事件
  const testBtn = div.querySelector('.test');
  const deleteBtn = div.querySelector('.delete');
  
  if (testBtn) {
    testBtn.addEventListener('click', (e) => {
      e.stopPropagation();
      testProfile(profile);
    });
  }
  
  if (deleteBtn) {
    deleteBtn.addEventListener('click', (e) => {
      e.stopPropagation();
      deleteProfile(profile.id);
    });
  }
  
  return div;
}

// 获取配置详情文本
function getProfileDetails(profile) {
  switch (profile.type) {
    case 'direct':
      return '直接连接，不使用代理';
    case 'system':
      return '使用系统代理设置';
    case 'fixed_servers':
      return `${profile.protocol?.toUpperCase() || 'HTTP'} ${profile.server}:${profile.port}`;
    case 'pac_script':
      return 'PAC脚本自动配置';
    default:
      return '未知类型';
  }
}

// 切换到指定配置
async function switchToProfile(profileId) {
  try {
    showLoading('切换中...');
    const response = await sendMessage({ 
      action: 'setCurrentProfile', 
      profileId: profileId 
    });
    
    if (response.success) {
      currentProfileId = profileId;
      updateStatusIndicator();
      renderProfiles();
      showSuccess('切换成功');
    } else {
      showError('切换失败: ' + response.error);
    }
  } catch (error) {
    console.error('Failed to switch profile:', error);
    showError('切换失败');
  }
}

// 更新状态指示器
function updateStatusIndicator() {
  const profile = currentProfiles[currentProfileId];
  if (profile) {
    const statusText = statusIndicator.querySelector('.status-text');
    const statusDot = statusIndicator.querySelector('.status-dot');
    
    statusText.textContent = profile.name;
    statusDot.style.backgroundColor = profile.color || '#28a745';
  }
}

// 显示添加配置模态框
function showAddProfileModal() {
  addProfileModal.style.display = 'block';
  document.getElementById('profileName').focus();
}

// 隐藏添加配置模态框
function hideAddProfileModal() {
  addProfileModal.style.display = 'none';
  addProfileForm.reset();
}

// 切换代理配置类型
function toggleProxyConfig() {
  const type = proxyType.value;
  
  if (type === 'fixed_servers') {
    fixedServerConfig.style.display = 'block';
    pacScriptConfig.style.display = 'none';
  } else if (type === 'pac_script') {
    fixedServerConfig.style.display = 'none';
    pacScriptConfig.style.display = 'block';
  }
}

// 处理添加配置表单提交
async function handleAddProfile(e) {
  e.preventDefault();
  
  try {
    const formData = new FormData(addProfileForm);
    const profile = {
      id: generateId(),
      name: document.getElementById('profileName').value,
      type: document.getElementById('proxyType').value,
      color: document.getElementById('profileColor').value
    };
    
    if (profile.type === 'fixed_servers') {
      profile.protocol = document.getElementById('proxyProtocol').value;
      profile.server = document.getElementById('proxyServer').value;
      profile.port = document.getElementById('proxyPort').value;
    } else if (profile.type === 'pac_script') {
      profile.pacScript = document.getElementById('pacScript').value;
    }
    
    showLoading('保存中...');
    const response = await sendMessage({ 
      action: 'saveProfile', 
      profile: profile 
    });
    
    if (response.success) {
      currentProfiles[profile.id] = profile;
      renderProfiles();
      hideAddProfileModal();
      showSuccess('配置已保存');
    } else {
      showError('保存失败: ' + response.error);
    }
  } catch (error) {
    console.error('Failed to add profile:', error);
    showError('保存失败');
  }
}

// 测试代理配置
async function testProfile(profile) {
  try {
    showLoading('测试中...');
    const response = await sendMessage({ 
      action: 'testProxy', 
      profile: profile 
    });
    
    if (response.success) {
      const result = response.data;
      if (result.success) {
        showSuccess(result.message);
      } else {
        showError(result.message);
      }
    }
  } catch (error) {
    console.error('Failed to test profile:', error);
    showError('测试失败');
  }
}

// 删除代理配置
async function deleteProfile(profileId) {
  if (!confirm('确定要删除这个配置吗？')) {
    return;
  }
  
  try {
    const response = await sendMessage({ 
      action: 'deleteProfile', 
      profileId: profileId 
    });
    
    if (response.success) {
      delete currentProfiles[profileId];
      renderProfiles();
      showSuccess('配置已删除');
    } else {
      showError('删除失败: ' + response.error);
    }
  } catch (error) {
    console.error('Failed to delete profile:', error);
    showError('删除失败');
  }
}

// 打开选项页面
function openOptionsPage() {
  chrome.runtime.openOptionsPage();
  window.close();
}

// 发送消息到背景脚本
function sendMessage(message) {
  return new Promise((resolve) => {
    chrome.runtime.sendMessage(message, resolve);
  });
}

// 生成唯一ID
function generateId() {
  return 'profile_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

// 显示状态信息
function showLoading(message) {
  connectionInfo.querySelector('.info-text').textContent = message;
  connectionInfo.style.color = '#007bff';
}

function showSuccess(message) {
  connectionInfo.querySelector('.info-text').textContent = message;
  connectionInfo.style.color = '#28a745';
  setTimeout(() => {
    connectionInfo.querySelector('.info-text').textContent = '准备就绪';
    connectionInfo.style.color = '#6c757d';
  }, 2000);
}

function showError(message) {
  connectionInfo.querySelector('.info-text').textContent = message;
  connectionInfo.style.color = '#dc3545';
  setTimeout(() => {
    connectionInfo.querySelector('.info-text').textContent = '准备就绪';
    connectionInfo.style.color = '#6c757d';
  }, 3000);
}
