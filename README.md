# Betterfly Proxy Manager

一个功能强大的Chrome浏览器代理管理插件，类似于Proxy SwitchyOmega，提供简单易用的代理配置和切换功能。

## 功能特性

- 🌐 **多种代理类型支持**：HTTP、HTTPS、SOCKS4、SOCKS5代理
- 📜 **PAC脚本支持**：支持PAC脚本自动配置
- ⚡ **快速切换**：一键切换不同的代理配置
- 🎯 **自动切换规则**：根据网站自动切换代理（开发中）
- 🔍 **连接测试**：测试代理服务器连接状态
- 💾 **配置管理**：导入导出配置，支持备份和恢复
- 🎨 **美观界面**：现代化的用户界面设计
- 🔔 **通知提醒**：代理切换状态通知

## 安装方法

### 开发者模式安装

1. 下载或克隆此项目到本地
2. 打开Chrome浏览器，进入 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择项目文件夹
6. 插件安装完成

### 生成图标文件

1. 在浏览器中打开 `generate-icons.html` 文件
2. 点击各个尺寸下的"下载"按钮
3. 将下载的文件重命名为 `icon16.png`, `icon32.png`, `icon48.png`, `icon128.png`
4. 将这些文件放入 `icons/` 目录中

## 使用指南

### 基本使用

1. **添加代理配置**
   - 点击插件图标打开弹出窗口
   - 点击"添加代理"按钮
   - 填写代理服务器信息
   - 保存配置

2. **切换代理**
   - 在弹出窗口中点击要使用的代理配置
   - 或者在选项页面中点击配置卡片

3. **管理配置**
   - 右键点击插件图标选择"选项"
   - 或者在弹出窗口中点击"选项"按钮
   - 在选项页面中可以编辑、删除、测试配置

### 代理类型说明

- **直接连接**：不使用任何代理，直接连接网络
- **系统代理**：使用系统的代理设置
- **HTTP/HTTPS代理**：使用HTTP或HTTPS协议的代理服务器
- **SOCKS代理**：使用SOCKS4或SOCKS5协议的代理服务器
- **PAC脚本**：使用PAC脚本自动配置代理

### 高级功能

- **配置导出**：在选项页面的"通用设置"中导出所有配置
- **配置导入**：导入之前导出的配置文件
- **连接测试**：测试代理服务器是否可用
- **自动切换**：根据访问的网站自动切换代理（开发中）

## 项目结构

```
betterfly-proxy/
├── manifest.json          # 插件清单文件
├── background.js          # 背景脚本
├── popup.html            # 弹出窗口页面
├── options.html          # 选项页面
├── generate-icons.html   # 图标生成工具
├── css/                  # 样式文件
│   ├── popup.css
│   └── options.css
├── js/                   # JavaScript文件
│   ├── proxy-manager.js  # 代理管理核心
│   ├── popup.js         # 弹出窗口脚本
│   └── options.js       # 选项页面脚本
├── icons/               # 图标文件
│   ├── icon.svg
│   ├── icon16.png
│   ├── icon32.png
│   ├── icon48.png
│   └── icon128.png
└── README.md           # 说明文档
```

## 开发说明

### 技术栈

- **Manifest V3**：使用最新的Chrome扩展API
- **Vanilla JavaScript**：原生JavaScript，无外部依赖
- **CSS3**：现代CSS特性，响应式设计
- **Chrome Extension APIs**：proxy, storage, notifications等

### 核心文件说明

- `manifest.json`：定义插件的基本信息和权限
- `background.js`：处理代理切换逻辑和消息通信
- `js/proxy-manager.js`：代理配置管理的核心类
- `popup.html/js`：快速切换界面
- `options.html/js`：详细配置管理界面

### 开发环境

1. 确保使用Chrome 88+版本
2. 开启开发者模式
3. 修改代码后刷新插件即可看到效果

## 常见问题

### Q: 代理设置不生效？
A: 请检查代理服务器地址和端口是否正确，可以使用"测试连接"功能验证。

### Q: 如何备份配置？
A: 在选项页面的"通用设置"中点击"导出配置"，保存JSON文件即可。

### Q: 支持哪些代理协议？
A: 支持HTTP、HTTPS、SOCKS4、SOCKS5代理协议，以及PAC脚本。

### Q: 如何自定义图标？
A: 编辑 `icons/icon.svg` 文件，然后使用 `generate-icons.html` 生成新的图标。

## 更新日志

### v1.0.0 (2025-07-02)
- 初始版本发布
- 支持基本的代理配置和切换功能
- 提供现代化的用户界面
- 支持配置导入导出
- 支持代理连接测试

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 致谢

本项目灵感来源于Proxy SwitchyOmega，感谢原作者的优秀工作。
