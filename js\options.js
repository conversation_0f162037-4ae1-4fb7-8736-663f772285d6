// DOM元素
const navItems = document.querySelectorAll('.nav-item');
const tabContents = document.querySelectorAll('.tab-content');
const profilesGrid = document.getElementById('profilesGrid');
const addNewProfile = document.getElementById('addNewProfile');
const profileModal = document.getElementById('profileModal');
const closeProfileModal = document.getElementById('closeProfileModal');
const cancelProfile = document.getElementById('cancelProfile');
const profileForm = document.getElementById('profileForm');
const testProfile = document.getElementById('testProfile');
const modalTitle = document.getElementById('modalTitle');

// 表单元素
const proxyType = document.getElementById('proxyType');
const fixedServerConfig = document.getElementById('fixedServerConfig');
const pacScriptConfig = document.getElementById('pacScriptConfig');
const enableAuth = document.getElementById('enableAuth');
const authFields = document.getElementById('authFields');

// 设置元素
const autoSwitchEnabled = document.getElementById('autoSwitchEnabled');
const showNotifications = document.getElementById('showNotifications');
const themeSelect = document.getElementById('theme');
const exportSettings = document.getElementById('exportSettings');
const importSettings = document.getElementById('importSettings');
const resetSettings = document.getElementById('resetSettings');
const importFileInput = document.getElementById('importFileInput');

// 全局变量
let currentProfiles = {};
let currentProfileId = 'direct';
let editingProfileId = null;

// 初始化
document.addEventListener('DOMContentLoaded', async () => {
  setupEventListeners();
  await loadProfiles();
  await loadCurrentProfile();
  await loadSettings();
  renderProfiles();
});

// 设置事件监听器
function setupEventListeners() {
  // 导航切换
  navItems.forEach(item => {
    item.addEventListener('click', (e) => {
      e.preventDefault();
      switchTab(item.dataset.tab);
    });
  });

  // 配置管理
  addNewProfile.addEventListener('click', showProfileModal);
  closeProfileModal.addEventListener('click', hideProfileModal);
  cancelProfile.addEventListener('click', hideProfileModal);
  profileForm.addEventListener('submit', handleProfileSubmit);
  testProfile.addEventListener('click', handleTestProfile);

  // 表单切换
  proxyType.addEventListener('change', toggleProxyConfig);
  enableAuth.addEventListener('change', toggleAuthFields);

  // 设置管理
  autoSwitchEnabled.addEventListener('change', saveSettings);
  showNotifications.addEventListener('change', saveSettings);
  themeSelect.addEventListener('change', saveSettings);
  exportSettings.addEventListener('click', handleExportSettings);
  importSettings.addEventListener('click', () => importFileInput.click());
  importFileInput.addEventListener('change', handleImportSettings);
  resetSettings.addEventListener('click', handleResetSettings);

  // 模态框外部点击关闭
  profileModal.addEventListener('click', (e) => {
    if (e.target === profileModal) {
      hideProfileModal();
    }
  });
}

// 切换标签页
function switchTab(tabId) {
  navItems.forEach(item => item.classList.remove('active'));
  tabContents.forEach(content => content.classList.remove('active'));
  
  document.querySelector(`[data-tab="${tabId}"]`).classList.add('active');
  document.getElementById(tabId).classList.add('active');
}

// 加载代理配置
async function loadProfiles() {
  try {
    const response = await sendMessage({ action: 'getProfiles' });
    if (response.success) {
      currentProfiles = response.data;
    }
  } catch (error) {
    console.error('Failed to load profiles:', error);
    showNotification('加载配置失败', 'error');
  }
}

// 加载当前配置
async function loadCurrentProfile() {
  try {
    const response = await sendMessage({ action: 'getCurrentProfile' });
    if (response.success) {
      currentProfileId = response.data;
    }
  } catch (error) {
    console.error('Failed to load current profile:', error);
  }
}

// 渲染代理配置
function renderProfiles() {
  profilesGrid.innerHTML = '';
  
  Object.values(currentProfiles).forEach(profile => {
    const card = createProfileCard(profile);
    profilesGrid.appendChild(card);
  });
}

// 创建配置卡片
function createProfileCard(profile) {
  const div = document.createElement('div');
  div.className = `profile-card ${profile.id === currentProfileId ? 'active' : ''}`;
  div.style.borderLeftColor = profile.color || '#007bff';
  
  const details = getProfileDetails(profile);
  const statusText = profile.id === currentProfileId ? '当前使用' : '未激活';
  
  div.innerHTML = `
    <div class="profile-header">
      <div>
        <div class="profile-title">${profile.name}</div>
        <div class="profile-type">${getProfileTypeName(profile.type)}</div>
      </div>
      <div class="profile-actions">
        ${!['direct', 'system'].includes(profile.id) ? `
          <button class="action-btn edit" title="编辑" data-id="${profile.id}">✏️</button>
          <button class="action-btn delete" title="删除" data-id="${profile.id}">🗑️</button>
        ` : ''}
      </div>
    </div>
    <div class="profile-details">${details}</div>
    <div class="profile-status">
      <span class="status-dot" style="background-color: ${profile.id === currentProfileId ? '#28a745' : '#6c757d'}"></span>
      <span>${statusText}</span>
    </div>
  `;
  
  // 添加事件监听器
  const editBtn = div.querySelector('.edit');
  const deleteBtn = div.querySelector('.delete');
  
  if (editBtn) {
    editBtn.addEventListener('click', () => editProfile(profile.id));
  }
  
  if (deleteBtn) {
    deleteBtn.addEventListener('click', () => deleteProfile(profile.id));
  }
  
  // 点击卡片切换配置
  div.addEventListener('click', (e) => {
    if (!e.target.classList.contains('action-btn')) {
      switchToProfile(profile.id);
    }
  });
  
  return div;
}

// 获取配置类型名称
function getProfileTypeName(type) {
  const typeNames = {
    'direct': '直接连接',
    'system': '系统代理',
    'fixed_servers': '固定代理',
    'pac_script': 'PAC脚本'
  };
  return typeNames[type] || '未知';
}

// 获取配置详情
function getProfileDetails(profile) {
  switch (profile.type) {
    case 'direct':
      return '直接连接，不使用代理服务器';
    case 'system':
      return '使用系统的代理设置';
    case 'fixed_servers':
      return `${profile.protocol?.toUpperCase() || 'HTTP'} ${profile.server}:${profile.port}`;
    case 'pac_script':
      return 'PAC脚本自动配置代理';
    default:
      return '未知配置类型';
  }
}

// 切换到指定配置
async function switchToProfile(profileId) {
  try {
    const response = await sendMessage({ 
      action: 'setCurrentProfile', 
      profileId: profileId 
    });
    
    if (response.success) {
      currentProfileId = profileId;
      renderProfiles();
      showNotification('代理配置已切换', 'success');
    } else {
      showNotification('切换失败: ' + response.error, 'error');
    }
  } catch (error) {
    console.error('Failed to switch profile:', error);
    showNotification('切换失败', 'error');
  }
}

// 显示配置模态框
function showProfileModal(profile = null) {
  editingProfileId = profile ? profile.id : null;
  modalTitle.textContent = profile ? '编辑代理配置' : '新建代理配置';
  
  if (profile) {
    fillProfileForm(profile);
  } else {
    profileForm.reset();
    document.getElementById('profileColor').value = '#007bff';
  }
  
  toggleProxyConfig();
  profileModal.style.display = 'block';
}

// 隐藏配置模态框
function hideProfileModal() {
  profileModal.style.display = 'none';
  editingProfileId = null;
  profileForm.reset();
}

// 填充配置表单
function fillProfileForm(profile) {
  document.getElementById('profileName').value = profile.name || '';
  document.getElementById('profileColor').value = profile.color || '#007bff';
  document.getElementById('proxyType').value = profile.type || 'fixed_servers';
  
  if (profile.type === 'fixed_servers') {
    document.getElementById('proxyProtocol').value = profile.protocol || 'http';
    document.getElementById('proxyServer').value = profile.server || '';
    document.getElementById('proxyPort').value = profile.port || '';
    document.getElementById('proxyUsername').value = profile.username || '';
    document.getElementById('proxyPassword').value = profile.password || '';
    
    if (profile.username) {
      document.getElementById('enableAuth').checked = true;
      toggleAuthFields();
    }
  } else if (profile.type === 'pac_script') {
    document.getElementById('pacScript').value = profile.pacScript || '';
  }
}

// 切换代理配置类型
function toggleProxyConfig() {
  const type = proxyType.value;
  
  if (type === 'fixed_servers') {
    fixedServerConfig.style.display = 'block';
    pacScriptConfig.style.display = 'none';
  } else if (type === 'pac_script') {
    fixedServerConfig.style.display = 'none';
    pacScriptConfig.style.display = 'block';
  }
}

// 切换身份验证字段
function toggleAuthFields() {
  authFields.style.display = enableAuth.checked ? 'block' : 'none';
}

// 处理配置表单提交
async function handleProfileSubmit(e) {
  e.preventDefault();
  
  try {
    const profile = {
      id: editingProfileId || generateId(),
      name: document.getElementById('profileName').value,
      type: document.getElementById('proxyType').value,
      color: document.getElementById('profileColor').value
    };
    
    if (profile.type === 'fixed_servers') {
      profile.protocol = document.getElementById('proxyProtocol').value;
      profile.server = document.getElementById('proxyServer').value;
      profile.port = document.getElementById('proxyPort').value;
      
      if (enableAuth.checked) {
        profile.username = document.getElementById('proxyUsername').value;
        profile.password = document.getElementById('proxyPassword').value;
      }
    } else if (profile.type === 'pac_script') {
      profile.pacScript = document.getElementById('pacScript').value;
    }
    
    const response = await sendMessage({ 
      action: 'saveProfile', 
      profile: profile 
    });
    
    if (response.success) {
      currentProfiles[profile.id] = profile;
      renderProfiles();
      hideProfileModal();
      showNotification(editingProfileId ? '配置已更新' : '配置已保存', 'success');
    } else {
      showNotification('保存失败: ' + response.error, 'error');
    }
  } catch (error) {
    console.error('Failed to save profile:', error);
    showNotification('保存失败', 'error');
  }
}

// 编辑配置
function editProfile(profileId) {
  const profile = currentProfiles[profileId];
  if (profile) {
    showProfileModal(profile);
  }
}

// 删除配置
async function deleteProfile(profileId) {
  if (!confirm('确定要删除这个配置吗？')) {
    return;
  }
  
  try {
    const response = await sendMessage({ 
      action: 'deleteProfile', 
      profileId: profileId 
    });
    
    if (response.success) {
      delete currentProfiles[profileId];
      renderProfiles();
      showNotification('配置已删除', 'success');
    } else {
      showNotification('删除失败: ' + response.error, 'error');
    }
  } catch (error) {
    console.error('Failed to delete profile:', error);
    showNotification('删除失败', 'error');
  }
}

// 测试配置
async function handleTestProfile() {
  try {
    const profile = {
      type: document.getElementById('proxyType').value,
      protocol: document.getElementById('proxyProtocol').value,
      server: document.getElementById('proxyServer').value,
      port: document.getElementById('proxyPort').value
    };
    
    if (profile.type === 'pac_script') {
      profile.pacScript = document.getElementById('pacScript').value;
    }
    
    showNotification('测试中...', 'info');
    const response = await sendMessage({ 
      action: 'testProxy', 
      profile: profile 
    });
    
    if (response.success) {
      const result = response.data;
      showNotification(result.message, result.success ? 'success' : 'error');
    }
  } catch (error) {
    console.error('Failed to test profile:', error);
    showNotification('测试失败', 'error');
  }
}

// 加载设置
async function loadSettings() {
  try {
    const result = await chrome.storage.sync.get(['settings']);
    const settings = result.settings || {};
    
    autoSwitchEnabled.checked = settings.autoSwitchEnabled !== false;
    showNotifications.checked = settings.showNotifications !== false;
    themeSelect.value = settings.theme || 'light';
  } catch (error) {
    console.error('Failed to load settings:', error);
  }
}

// 保存设置
async function saveSettings() {
  try {
    const settings = {
      autoSwitchEnabled: autoSwitchEnabled.checked,
      showNotifications: showNotifications.checked,
      theme: themeSelect.value
    };
    
    await chrome.storage.sync.set({ settings });
    showNotification('设置已保存', 'success');
  } catch (error) {
    console.error('Failed to save settings:', error);
    showNotification('保存设置失败', 'error');
  }
}

// 导出设置
function handleExportSettings() {
  const data = {
    profiles: currentProfiles,
    settings: {
      autoSwitchEnabled: autoSwitchEnabled.checked,
      showNotifications: showNotifications.checked,
      theme: themeSelect.value
    },
    exportDate: new Date().toISOString()
  };
  
  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `betterfly-proxy-config-${new Date().toISOString().split('T')[0]}.json`;
  a.click();
  URL.revokeObjectURL(url);
  
  showNotification('配置已导出', 'success');
}

// 导入设置
function handleImportSettings(e) {
  const file = e.target.files[0];
  if (!file) return;
  
  const reader = new FileReader();
  reader.onload = async (e) => {
    try {
      const data = JSON.parse(e.target.result);
      
      if (data.profiles) {
        // 保存导入的配置
        for (const profile of Object.values(data.profiles)) {
          if (!['direct', 'system'].includes(profile.id)) {
            await sendMessage({ action: 'saveProfile', profile });
          }
        }
        await loadProfiles();
        renderProfiles();
      }
      
      if (data.settings) {
        autoSwitchEnabled.checked = data.settings.autoSwitchEnabled !== false;
        showNotifications.checked = data.settings.showNotifications !== false;
        themeSelect.value = data.settings.theme || 'light';
        await saveSettings();
      }
      
      showNotification('配置导入成功', 'success');
    } catch (error) {
      console.error('Failed to import settings:', error);
      showNotification('导入失败，请检查文件格式', 'error');
    }
  };
  reader.readAsText(file);
}

// 重置设置
async function handleResetSettings() {
  if (!confirm('确定要重置所有设置吗？这将删除所有自定义配置。')) {
    return;
  }
  
  try {
    // 删除所有自定义配置
    for (const profileId of Object.keys(currentProfiles)) {
      if (!['direct', 'system'].includes(profileId)) {
        await sendMessage({ action: 'deleteProfile', profileId });
      }
    }
    
    // 重置设置
    await chrome.storage.sync.clear();
    await chrome.storage.local.clear();
    
    // 重新加载
    await loadProfiles();
    await loadSettings();
    renderProfiles();
    
    showNotification('设置已重置', 'success');
  } catch (error) {
    console.error('Failed to reset settings:', error);
    showNotification('重置失败', 'error');
  }
}

// 发送消息到背景脚本
function sendMessage(message) {
  return new Promise((resolve) => {
    chrome.runtime.sendMessage(message, resolve);
  });
}

// 生成唯一ID
function generateId() {
  return 'profile_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

// 显示通知
function showNotification(message, type = 'info') {
  // 创建通知元素
  const notification = document.createElement('div');
  notification.className = `notification notification-${type}`;
  notification.textContent = message;
  
  // 添加样式
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 6px;
    color: white;
    font-weight: 500;
    z-index: 10000;
    animation: slideIn 0.3s ease;
  `;
  
  // 设置背景颜色
  const colors = {
    success: '#28a745',
    error: '#dc3545',
    info: '#17a2b8',
    warning: '#ffc107'
  };
  notification.style.backgroundColor = colors[type] || colors.info;
  
  // 添加到页面
  document.body.appendChild(notification);
  
  // 3秒后自动移除
  setTimeout(() => {
    notification.style.animation = 'slideOut 0.3s ease';
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 300);
  }, 3000);
}

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
  @keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
  }
  
  @keyframes slideOut {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
  }
`;
document.head.appendChild(style);
