<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 128 128" width="128" height="128">
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="64" cy="64" r="60" fill="url(#gradient)" stroke="#fff" stroke-width="4"/>
  
  <!-- 代理图标 - 网络节点 -->
  <g fill="#fff" stroke="#fff" stroke-width="2">
    <!-- 中心节点 -->
    <circle cx="64" cy="64" r="8" fill="#fff"/>
    
    <!-- 周围节点 -->
    <circle cx="32" cy="32" r="6" fill="#fff"/>
    <circle cx="96" cy="32" r="6" fill="#fff"/>
    <circle cx="32" cy="96" r="6" fill="#fff"/>
    <circle cx="96" cy="96" r="6" fill="#fff"/>
    
    <!-- 连接线 -->
    <line x1="32" y1="32" x2="58" y2="58" stroke="#fff" stroke-width="3" opacity="0.8"/>
    <line x1="96" y1="32" x2="70" y2="58" stroke="#fff" stroke-width="3" opacity="0.8"/>
    <line x1="32" y1="96" x2="58" y2="70" stroke="#fff" stroke-width="3" opacity="0.8"/>
    <line x1="96" y1="96" x2="70" y2="70" stroke="#fff" stroke-width="3" opacity="0.8"/>
    
    <!-- 数据流动效果 -->
    <circle cx="45" cy="45" r="2" fill="#fff" opacity="0.6">
      <animateTransform attributeName="transform" type="translate" 
        values="0,0; 19,19; 0,0" dur="2s" repeatCount="indefinite"/>
    </circle>
    <circle cx="83" cy="45" r="2" fill="#fff" opacity="0.6">
      <animateTransform attributeName="transform" type="translate" 
        values="0,0; -19,19; 0,0" dur="2s" repeatCount="indefinite"/>
    </circle>
  </g>
  
  <!-- 文字标识 -->
  <text x="64" y="110" text-anchor="middle" fill="#fff" font-family="Arial, sans-serif" font-size="12" font-weight="bold">PROXY</text>
</svg>
