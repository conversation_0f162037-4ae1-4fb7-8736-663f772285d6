# Betterfly Proxy Manager 安装指南

## 快速安装步骤

### 1. 生成图标文件

在安装插件之前，需要先生成图标文件：

1. 在浏览器中打开 `generate-icons.html` 文件
2. 等待页面加载完成，会自动生成预览图标
3. 依次点击每个尺寸下的"下载"按钮：
   - 下载 16x16 图标，重命名为 `icon16.png`
   - 下载 32x32 图标，重命名为 `icon32.png`
   - 下载 48x48 图标，重命名为 `icon48.png`
   - 下载 128x128 图标，重命名为 `icon128.png`
4. 将这4个PNG文件放入 `icons/` 目录中

### 2. 安装Chrome插件

1. 打开Chrome浏览器
2. 在地址栏输入 `chrome://extensions/` 并回车
3. 在页面右上角开启"开发者模式"开关
4. 点击"加载已解压的扩展程序"按钮
5. 选择整个项目文件夹（包含manifest.json的文件夹）
6. 点击"选择文件夹"

### 3. 验证安装

安装成功后，你应该能看到：
- Chrome工具栏中出现Betterfly Proxy图标
- 插件列表中显示"Betterfly Proxy Manager"
- 点击图标可以打开弹出窗口

## 使用前准备

### 首次使用

1. 点击Chrome工具栏中的插件图标
2. 点击"添加代理"按钮
3. 填写你的代理服务器信息：
   - 配置名称：给代理起个名字
   - 代理类型：选择HTTP/HTTPS/SOCKS代理
   - 服务器地址：代理服务器IP或域名
   - 端口：代理服务器端口号
   - 协议：选择HTTP、HTTPS、SOCKS4或SOCKS5
4. 点击"保存配置"

### 测试代理

1. 在配置列表中找到刚添加的代理
2. 点击测试按钮（🔍图标）
3. 等待测试结果，确保连接正常

### 切换代理

1. 在弹出窗口中点击要使用的代理配置
2. 观察状态指示器变化
3. 访问网站验证代理是否生效

## 高级配置

### 选项页面

右键点击插件图标，选择"选项"，或在弹出窗口中点击"选项"按钮，可以：

- 管理所有代理配置
- 编辑现有配置
- 导入/导出配置
- 调整插件设置

### PAC脚本配置

如果你有PAC脚本：

1. 在添加代理时选择"PAC脚本"类型
2. 在文本框中输入PAC脚本内容或URL
3. 保存并测试配置

### 配置备份

定期备份你的配置：

1. 进入选项页面
2. 点击"导出配置"按钮
3. 保存JSON文件到安全位置

## 故障排除

### 插件无法加载

- 确保所有文件都在正确位置
- 检查manifest.json文件是否完整
- 确保已生成所有图标文件

### 代理不生效

- 检查代理服务器地址和端口
- 使用测试功能验证连接
- 确认代理服务器正常运行

### 图标显示异常

- 重新生成图标文件
- 确保图标文件命名正确
- 刷新插件或重新加载

## 卸载插件

如需卸载：

1. 进入 `chrome://extensions/`
2. 找到"Betterfly Proxy Manager"
3. 点击"移除"按钮
4. 确认卸载

## 技术支持

如果遇到问题：

1. 检查Chrome控制台是否有错误信息
2. 确认Chrome版本支持Manifest V3
3. 查看README.md中的常见问题部分

---

安装完成后，你就可以享受便捷的代理管理体验了！
