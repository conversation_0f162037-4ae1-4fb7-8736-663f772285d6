<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>生成图标</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      padding: 20px;
      background: #f5f5f5;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .icon-preview {
      display: flex;
      gap: 20px;
      margin: 20px 0;
      align-items: center;
    }
    .icon-size {
      text-align: center;
    }
    .icon-size img {
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    .download-btn {
      background: #007bff;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      margin-top: 10px;
    }
    .download-btn:hover {
      background: #0056b3;
    }
    .instructions {
      background: #e9ecef;
      padding: 15px;
      border-radius: 4px;
      margin-top: 20px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Betterfly Proxy Manager - 图标生成器</h1>
    <p>这个工具可以帮助你生成不同尺寸的插件图标。</p>
    
    <div class="icon-preview">
      <div class="icon-size">
        <h3>16x16</h3>
        <canvas id="canvas16" width="16" height="16"></canvas>
        <br>
        <button class="download-btn" onclick="downloadIcon(16)">下载</button>
      </div>
      
      <div class="icon-size">
        <h3>32x32</h3>
        <canvas id="canvas32" width="32" height="32"></canvas>
        <br>
        <button class="download-btn" onclick="downloadIcon(32)">下载</button>
      </div>
      
      <div class="icon-size">
        <h3>48x48</h3>
        <canvas id="canvas48" width="48" height="48"></canvas>
        <br>
        <button class="download-btn" onclick="downloadIcon(48)">下载</button>
      </div>
      
      <div class="icon-size">
        <h3>128x128</h3>
        <canvas id="canvas128" width="128" height="128"></canvas>
        <br>
        <button class="download-btn" onclick="downloadIcon(128)">下载</button>
      </div>
    </div>
    
    <div class="instructions">
      <h3>使用说明：</h3>
      <ol>
        <li>点击上方的"下载"按钮下载对应尺寸的图标</li>
        <li>将下载的图标文件重命名为 icon16.png, icon32.png, icon48.png, icon128.png</li>
        <li>将这些文件放入 icons/ 目录中</li>
        <li>如果需要自定义图标，可以编辑 icons/icon.svg 文件</li>
      </ol>
    </div>
  </div>

  <script>
    // SVG图标内容
    const svgContent = `
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 128 128" width="128" height="128">
        <defs>
          <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
          </linearGradient>
        </defs>
        
        <circle cx="64" cy="64" r="60" fill="url(#gradient)" stroke="#fff" stroke-width="4"/>
        
        <g fill="#fff" stroke="#fff" stroke-width="2">
          <circle cx="64" cy="64" r="8" fill="#fff"/>
          <circle cx="32" cy="32" r="6" fill="#fff"/>
          <circle cx="96" cy="32" r="6" fill="#fff"/>
          <circle cx="32" cy="96" r="6" fill="#fff"/>
          <circle cx="96" cy="96" r="6" fill="#fff"/>
          
          <line x1="32" y1="32" x2="58" y2="58" stroke="#fff" stroke-width="3" opacity="0.8"/>
          <line x1="96" y1="32" x2="70" y2="58" stroke="#fff" stroke-width="3" opacity="0.8"/>
          <line x1="32" y1="96" x2="58" y2="70" stroke="#fff" stroke-width="3" opacity="0.8"/>
          <line x1="96" y1="96" x2="70" y2="70" stroke="#fff" stroke-width="3" opacity="0.8"/>
        </g>
        
        <text x="64" y="110" text-anchor="middle" fill="#fff" font-family="Arial, sans-serif" font-size="12" font-weight="bold">PROXY</text>
      </svg>
    `;

    // 生成图标
    function generateIcon(size) {
      const canvas = document.getElementById(`canvas${size}`);
      const ctx = canvas.getContext('2d');
      
      // 创建SVG图像
      const img = new Image();
      const svgBlob = new Blob([svgContent], { type: 'image/svg+xml' });
      const url = URL.createObjectURL(svgBlob);
      
      img.onload = function() {
        ctx.clearRect(0, 0, size, size);
        ctx.drawImage(img, 0, 0, size, size);
        URL.revokeObjectURL(url);
      };
      
      img.src = url;
    }

    // 下载图标
    function downloadIcon(size) {
      const canvas = document.getElementById(`canvas${size}`);
      const link = document.createElement('a');
      link.download = `icon${size}.png`;
      link.href = canvas.toDataURL('image/png');
      link.click();
    }

    // 页面加载时生成所有图标
    window.onload = function() {
      generateIcon(16);
      generateIcon(32);
      generateIcon(48);
      generateIcon(128);
    };
  </script>
</body>
</html>
